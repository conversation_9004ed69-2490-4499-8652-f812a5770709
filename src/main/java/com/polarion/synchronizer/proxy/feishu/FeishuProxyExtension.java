package com.polarion.synchronizer.proxy.feishu;

import com.polarion.core.util.logging.Logger;
import com.polarion.synchronizer.IProxyConfiguration;
import com.polarion.synchronizer.configuration.IConnection;
import com.polarion.synchronizer.model.IProxy;
import com.polarion.synchronizer.spi.IProxyExtension;
import org.jetbrains.annotations.NotNull;

/**
 * 飞书代理扩展
 * 用于在代理创建时进行自定义处理，如注入默认字段映射等
 */
public class FeishuProxyExtension implements IProxyExtension {
    
    private static final Logger logger = Logger.getLogger(FeishuProxyExtension.class);
    
    @NotNull
    @Override
    public IProxy applyToProxy(@NotNull IProxy proxy, @NotNull IProxyConfiguration<?> configuration) {
        // 如果是飞书代理，可以在这里进行自定义处理
        if (proxy instanceof FeishuProxy) {
            logger.debug("应用飞书代理扩展: " + configuration.getClass().getSimpleName());
            // 这里可以添加额外的处理逻辑
        }
        
        return proxy;
    }
    
    @NotNull
    @Override
    public <T extends IConnection> IProxyConfiguration<T> applyToConfiguration(IProxyConfiguration<T> configuration) {
        // 如果是飞书配置，可以在这里进行自定义处理
        if (configuration instanceof FeishuProxyConfiguration) {
            logger.debug("应用飞书配置扩展: " + configuration.getClass().getSimpleName());
            // 这里可以添加额外的处理逻辑
        }
        
        return configuration;
    }
}
