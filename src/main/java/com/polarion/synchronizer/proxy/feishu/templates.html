<!-- 飞书项目代理配置模板 -->
<script type="text/x-handlebars" data-template-name="feishuProxyConfiguration">
  <div class="control-group">
    <label class="control-label"><span style="color:red">*</span>项目Key:</label>
    <div class="controls">
      {{view Ember.TextField valueBinding="projectKey" placeholder="输入飞书项目的project_key"}}
      <span class="help-inline">飞书项目的唯一标识符</span>
    </div>
  </div>
  
  <div class="control-group">
    <label class="control-label">工作项类型:</label>
    <div class="controls">
      {{view Ember.TextField valueBinding="workItemTypes" placeholder="story,task,bug"}}
      <span class="help-inline">要同步的工作项类型，多个类型用逗号分隔</span>
    </div>
  </div>

  <div class="control-group">
    <label class="control-label">排除图表类型:</label>
    <div class="controls">
      {{view Ember.Checkbox checkedBinding="excludeChartType"}}
      <span class="help-inline">排除图表类型以优化通用字段计算（推荐开启）</span>
    </div>
  </div>

</script>

<!-- 飞书项目连接配置模板 -->
<script type="text/x-handlebars" data-template-name="feishuConnection" style="display:table-row-group">
  <div {{bindAttr class="view.hasCreateError:error :control-group"}}>
    <label class="control-label"><span style="color:red">*</span>Server URL:</label>
    <div class="controls">
      {{view Ember.TextField valueBinding="controller.content.serverUrl"
             disabledBinding="controller.readOnly"
             placeholder="https://project.feishu.cn"}}
      <span class="help-inline">飞书项目服务器地址</span>
    </div>
  </div>

  <div {{bindAttr class="view.hasCreateError:error :control-group"}}>
    <label class="control-label"><span style="color:red">*</span>访问凭证类型:</label>
    <div class="controls">
      <select {{bindAttr value="controller.content.authMode"}}>
        <option value="plugin_access_token">插件访问凭证 (App ID + App Secret)</option>
        <option value="virtual_plugin_token">虚拟插件访问凭证 (开发调试用)</option>
      </select>
      <span class="help-inline">选择飞书项目访问凭证类型</span>
    </div>
  </div>

  {{#if controller.isPluginAccessToken}}
  <div {{bindAttr class="view.hasCreateError:error :control-group"}}>
    <label class="control-label"><span style="color:red">*</span>App ID:</label>
    <div class="controls">
      {{view Ember.TextField valueBinding="controller.content.pluginId"
             placeholder="cli_a7fbxxxxxxxxxxxx"}}
      <span class="help-inline">飞书项目应用的唯一标识符 (app_id)</span>
    </div>
  </div>

  <div {{bindAttr class="view.hasCreateError:error :control-group"}}>
    <label class="control-label"><span style="color:red">*</span>App Secret:</label>
    <div class="controls">
      {{view Ember.TextField valueBinding="controller.content.password"
             type="password"
             placeholder="输入应用密钥"}}
      <span class="help-inline">应用密钥 (app_secret)，用于获取插件访问凭证</span>
    </div>
  </div>
  {{/if}}

  {{#if controller.isVirtualPluginToken}}
  <div {{bindAttr class="view.hasCreateError:error :control-group"}}>
    <label class="control-label"><span style="color:red">*</span>Virtual Plugin Token:</label>
    <div class="controls">
      {{view Ember.TextField valueBinding="controller.content.password"
             type="password"
             placeholder="v-b0dc4a7d-9080-4bc3-b54a-76b68056****"}}
      <span class="help-inline">虚拟插件访问凭证，仅供开发调试使用</span>
    </div>
  </div>
  {{/if}}

  {{#if controller.isUserAccessToken}}
  <div {{bindAttr class="view.hasCreateError:error :control-group"}}>
    <label class="control-label"><span style="color:red">*</span>User Access Token:</label>
    <div class="controls">
      {{view Ember.TextField valueBinding="controller.content.password"
             type="password"
             placeholder="u-f09b09b8-e78a-405b-9733-7261787e****"}}
      <span class="help-inline">用户访问凭证，代表用户对插件的临时授权</span>
    </div>
  </div>
  {{/if}}

  <div class="control-group">
    <div class="controls">
      <button type="button" class="btn btn-primary" {{action "testConnection" target="controller"}}>
        测试连接
      </button>
      <span class="help-inline">点击测试与飞书项目的连接是否正常</span>
    </div>
  </div>
</script>
